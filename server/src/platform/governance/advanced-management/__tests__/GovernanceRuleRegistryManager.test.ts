/**
 * @file Governance Rule Registry Manager Test Suite
 * @filepath server/src/platform/governance/advanced-management/__tests__/GovernanceRuleRegistryManager.test.ts
 * @task-id G-TSK-04.SUB-04.4.TEST-REGISTRY
 * @component governance-rule-registry-manager-tests
 * @reference governance-context.REGISTRY.TEST.001
 * @template enterprise-test-suite
 * @tier T1
 * @context governance-context
 * @category Advanced Management Tests
 * @created 2025-08-31
 * @modified 2025-08-31
 * 
 * @description
 * Comprehensive test suite for GovernanceRuleRegistryManager using OA Framework
 * enterprise standards and proven surgical precision testing techniques:
 * - Constructor and initialization testing with BaseTrackingService compliance
 * - Rule registration and validation with comprehensive error handling
 * - Rule discovery and search mechanisms with performance optimization
 * - Registry management operations (add, remove, update, list)
 * - Advanced search and filtering capabilities with caching
 * - Batch validation and processing with enterprise-scale datasets
 * - Export/import functionality with multiple format support
 * - Error handling and edge case coverage using surgical precision patterns
 * - Memory safety validation with extended operation tests
 * - Performance and scalability testing with enterprise requirements
 * 
 * @coverage-target 95%+ using surgical precision testing techniques
 * @test-framework Jest with OA Framework patterns
 * @memory-safety BaseTrackingService inheritance with proper cleanup
 * 
 * <AUTHOR> Consultancy - Advanced Governance Testing Team
 * @version 1.0.0
 * @since 2025-08-31
 */

// ============================================================================
// IMPORTS AND DEPENDENCIES
// ============================================================================

// Testing Framework
import { jest, describe, beforeEach, afterEach, it, expect } from '@jest/globals';

// Component Under Test
import { 
  GovernanceRuleRegistryManager,
  IGovernanceRuleRegistryManager 
} from '../GovernanceRuleRegistryManager';

// Type Definitions
import {
  TGovernanceRule,
  TGovernanceRuleSet,
  TGovernanceRuleType,
  TGovernanceRuleSeverity,
  TExecutionContext,
  TExecutionEnvironment
} from '../../../../../../shared/src/types/platform/governance/rule-management-types';

import {
  TValidationResult,
  TTrackingConfig,
  TMetrics,
  TComponentStatus
} from '../../../../../../shared/src/types/platform/tracking/tracking-types';

// Test Utilities
import { 
  ResilientTimer 
} from '../../../../../../shared/src/base/utils/ResilientTiming';
import { 
  ResilientMetricsCollector 
} from '../../../../../../shared/src/base/utils/ResilientMetrics';

// ============================================================================
// TEST CONFIGURATION AND CONSTANTS
// ============================================================================

/**
 * Test configuration constants
 */
const TEST_CONFIG = {
  // Test timeouts
  DEFAULT_TIMEOUT: 30000, // 30 seconds
  LONG_RUNNING_TIMEOUT: 120000, // 2 minutes for memory tests
  
  // Test data sizes
  SMALL_REGISTRY_SIZE: 10,
  MEDIUM_REGISTRY_SIZE: 100,
  LARGE_REGISTRY_SIZE: 1000,
  ENTERPRISE_REGISTRY_SIZE: 5000,
  
  // Performance thresholds
  MAX_REGISTRATION_TIME: 1000, // 1 second
  MAX_SEARCH_TIME: 2000, // 2 seconds
  MAX_VALIDATION_TIME: 3000, // 3 seconds
  
  // Memory thresholds
  MAX_MEMORY_GROWTH_MB: 100,
  MEMORY_LEAK_ITERATIONS: 50
} as const;

/**
 * Mock data factories
 */
class TestDataFactory {
  /**
   * Create mock governance rule
   */
  static createMockRule(
    ruleId: string,
    type: TGovernanceRuleType = 'compliance-check',
    category: string = 'test-category',
    priority: number = 5,
    tags: string[] = ['test']
  ): TGovernanceRule {
    return {
      ruleId,
      name: `Test Rule ${ruleId}`,
      description: `Test governance rule for ${ruleId}`,
      type,
      category,
      severity: 'warning' as TGovernanceRuleSeverity,
      priority,
      configuration: {
        parameters: { testParam: 'testValue' },
        criteria: {
          type: 'validation',
          expression: 'testField === testValue',
          expectedValues: ['testValue'],
          operators: ['==='],
          weight: 1
        },
        actions: [{
          type: 'log',
          configuration: { message: 'Test action' },
          priority: 1
        }],
        dependencies: []
      },
      metadata: {
        version: '1.0.0',
        author: 'Test Suite',
        createdAt: new Date(),
        modifiedAt: new Date(),
        tags,
        documentation: []
      },
      status: {
        current: 'active',
        activatedAt: new Date(),
        effectiveness: 100
      }
    };
  }
  
  /**
   * Create rule set with specified characteristics
   */
  static createRuleSet(
    size: number,
    typeDistribution?: Partial<Record<TGovernanceRuleType, number>>,
    categoryPrefix: string = 'category'
  ): TGovernanceRule[] {
    const rules: TGovernanceRule[] = [];
    const types: TGovernanceRuleType[] = [
      'compliance-check',
      'security-policy',
      'access-control',
      'audit-requirement',
      'quality-standard'
    ];
    
    for (let i = 0; i < size; i++) {
      const type = types[i % types.length];
      const category = `${categoryPrefix}-${Math.floor(i / 10)}`;
      const priority = Math.floor(Math.random() * 10) + 1;
      const tags = [`tag-${i % 5}`, `group-${Math.floor(i / 20)}`];
      
      rules.push(TestDataFactory.createMockRule(
        `rule-${i.toString().padStart(4, '0')}`,
        type,
        category,
        priority,
        tags
      ));
    }
    
    return rules;
  }
  
  /**
   * Create search criteria for testing
   */
  static createSearchCriteria(overrides: any = {}): any {
    return {
      query: 'test',
      type: 'compliance-check' as TGovernanceRuleType,
      category: 'test-category',
      severity: 'warning' as TGovernanceRuleSeverity,
      tags: ['test'],
      keywords: ['test'],
      priorityRange: { min: 1, max: 10 },
      limit: 100,
      offset: 0,
      sortBy: 'name' as const,
      sortOrder: 'asc' as const,
      ...overrides
    };
  }
}

// ============================================================================
// TEST SUITE SETUP
// ============================================================================

describe('GovernanceRuleRegistryManager', () => {
  let registryManager: GovernanceRuleRegistryManager;
  let mockConfig: Partial<TTrackingConfig>;
  
  // Memory tracking for leak detection
  let initialMemoryUsage: number;
  
  beforeEach(async () => {
    // Track initial memory usage
    if (global.gc) {
      global.gc();
    }
    initialMemoryUsage = process.memoryUsage().heapUsed;
    
    // Create mock configuration
    mockConfig = {
      service: {
        name: 'test-registry-manager',
        version: '1.0.0',
        environment: 'development',
        timeout: 30000,
        retry: {
          maxAttempts: 3,
          delay: 1000,
          backoffMultiplier: 2,
          maxDelay: 5000
        }
      },
      governance: {
        authority: 'Test Authority',
        requiredCompliance: ['test-compliance'],
        auditFrequency: 24,
        violationReporting: true
      },
      performance: {
        metricsEnabled: true,
        metricsInterval: 30000,
        monitoringEnabled: true,
        alertThresholds: {
          responseTime: 5000,
          errorRate: 0.05,
          memoryUsage: 0.8,
          cpuUsage: 0.8
        }
      }
    };
    
    // Create registry manager instance
    registryManager = new GovernanceRuleRegistryManager(mockConfig);
    await registryManager.initialize();
  });
  
  afterEach(async () => {
    // Cleanup registry manager
    if (registryManager) {
      await registryManager.shutdown();
    }
    
    // Check for memory leaks
    if (global.gc) {
      global.gc();
    }
    
    const finalMemoryUsage = process.memoryUsage().heapUsed;
    const memoryGrowth = (finalMemoryUsage - initialMemoryUsage) / 1024 / 1024; // MB
    
    if (memoryGrowth > TEST_CONFIG.MAX_MEMORY_GROWTH_MB) {
      console.warn(`Potential memory leak detected: ${memoryGrowth.toFixed(2)}MB growth`);
    }
  });

  // ============================================================================
  // CONSTRUCTOR AND INITIALIZATION TESTING
  // ============================================================================

  describe('Constructor and Initialization', () => {
    it('should initialize with BaseTrackingService inheritance', async () => {
      expect(registryManager).toBeInstanceOf(GovernanceRuleRegistryManager);
      expect(registryManager.id).toBeDefined();
      expect(registryManager.authority).toBeDefined();
      expect(registryManager.authority).toContain('E.Z. Consultancy');
    });

    it('should initialize resilient timing infrastructure in constructor', () => {
      // ✅ SURGICAL PRECISION: Test resilient timing initialization
      // Access private properties using type assertion for testing
      const manager = registryManager as any;

      expect(manager._resilientTimer).toBeDefined();
      expect(manager._metricsCollector).toBeDefined();
      expect(manager._resilientTimer).toBeInstanceOf(ResilientTimer);
      expect(manager._metricsCollector).toBeInstanceOf(ResilientMetricsCollector);
    });

    it('should initialize with governance-specific timing thresholds', () => {
      // ✅ SURGICAL PRECISION: Test governance timing configuration (5000ms/50ms)
      const manager = registryManager as any;
      const timerConfig = manager._resilientTimer.config;

      expect(timerConfig.maxExpectedDuration).toBe(5000);
      expect(timerConfig.estimateBaseline).toBe(50);
      expect(timerConfig.enableFallbacks).toBe(true);
    });
